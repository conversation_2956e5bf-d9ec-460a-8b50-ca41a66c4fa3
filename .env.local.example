# Environment Variables for PromptPay QR Generator
# Copy this file to .env.local and fill in your values

# Application Configuration
NEXT_PUBLIC_APP_NAME="PromptPay QR Generator"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:3000"

# QR Code Configuration (Optional)
QR_DEFAULT_WIDTH=256
QR_DEFAULT_QUALITY=0.92
QR_DEFAULT_MARGIN=1

# Vercel Analytics (Optional)
# NEXT_PUBLIC_VERCEL_ANALYTICS_ID=""

# Rate Limiting (Optional)
# API_RATE_LIMIT_REQUESTS=100
# API_RATE_LIMIT_WINDOW=900000
