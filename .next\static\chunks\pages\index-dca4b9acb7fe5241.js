(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{546:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},1393:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=a(4232),l=n.useLayoutEffect,r=n.useEffect;function s(e){let{headManager:t,reduceComponentsToState:a}=e;function s(){if(t&&t.mountedInstances){let l=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(a(l,e))}}return l(()=>{var a;return null==t||null==(a=t.mountedInstances)||a.add(e.children),()=>{var a;null==t||null==(a=t.mountedInstances)||a.delete(e.children)}}),l(()=>(t&&(t._pendingUpdate=s),()=>{t&&(t._pendingUpdate=s)})),r(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},1935:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var n=a(7876),l=a(7328),r=a.n(l),s=a(4232);function i(){let[e,t]=(0,s.useState)({type:"",identifier:"",amount:"",merchantName:"",merchantCity:""}),[a,l]=(0,s.useState)(null),[r,i]=(0,s.useState)(!1),[o,d]=(0,s.useState)(""),[c,m]=(0,s.useState)(""),u=e=>{let{name:a,value:n}=e.target;t(e=>({...e,[a]:n})),"type"===a&&t(e=>({...e,identifier:""}))},p=()=>{let{type:t,identifier:a}=e;if(!t)return"Please select a payment type.";if(!a.trim())return"Identifier is required.";if("national_id"===t){if(13!==a.replace(/\D/g,"").length)return"National ID must be exactly 13 digits."}else if("ewallet"===t){if(15!==a.replace(/\D/g,"").length)return"E-Wallet ID must be exactly 15 digits."}else if("mobile"===t){let e=a.replace(/\D/g,"");if(e.length<9||e.length>10)return"Mobile number should be 9 or 10 digits."}return null},h=async t=>{t.preventDefault(),d(""),m("");let a=p();if(a)return void d(a);i(!0);try{let t={type:e.type,identifier:e.identifier,amount:e.amount?parseFloat(e.amount):null,merchantName:e.merchantName||null,merchantCity:e.merchantCity||null},a=await fetch("/api/generate-qr",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),n=await a.json();n.success?(l(n.data),m("QR Code generated successfully!")):d(n.error||"Failed to generate QR code")}catch(e){console.error("Error:",e),d("Network error. Please try again.")}finally{i(!1)}},f=async()=>{if(a)try{await navigator.clipboard.writeText(a.qrCodeBase64),m("Base64 data copied to clipboard!")}catch(t){let e=document.createElement("textarea");e.value=a.qrCodeBase64,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),m("Base64 data copied to clipboard!")}},x=(()=>{switch(e.type){case"mobile":return{label:"Mobile Number",placeholder:"e.g., 0812345678 or +66812345678",help:"Enter Thai mobile number (with or without country code)",type:"tel"};case"national_id":return{label:"National ID",placeholder:"e.g., 1234567890123",help:"Enter 13-digit Thai National ID (with or without dashes)",type:"text"};case"ewallet":return{label:"E-Wallet ID",placeholder:"e.g., 1234567890",help:"Enter your e-wallet identifier",type:"text"};default:return{label:"Identifier",placeholder:"Enter identifier",help:"Please select a payment type first",type:"text"}}})();return(0,n.jsxs)("div",{className:"container max-w-4xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden",children:[(0,n.jsxs)("div",{className:"bg-gradient-to-r from-blue-400 to-cyan-300 text-white p-8 text-center",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-2",children:"PromptPay QR Generator"}),(0,n.jsx)("p",{className:"text-lg opacity-90",children:"Generate QR codes for Thai PromptPay payments instantly"})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 min-h-[600px]",children:[(0,n.jsx)("div",{className:"p-8 bg-gray-50",children:(0,n.jsxs)("form",{onSubmit:h,children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("label",{htmlFor:"type",className:"block font-semibold text-gray-700 mb-2",children:["Payment Type ",(0,n.jsx)("span",{className:"text-red-600",children:"*"})]}),(0,n.jsxs)("select",{id:"type",name:"type",value:e.type,onChange:u,required:!0,className:"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400",children:[(0,n.jsx)("option",{value:"",children:"Select payment type"}),(0,n.jsx)("option",{value:"mobile",children:"Mobile Number"}),(0,n.jsx)("option",{value:"national_id",children:"National ID"}),(0,n.jsx)("option",{value:"ewallet",children:"E-Wallet ID"})]})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("label",{htmlFor:"identifier",className:"block font-semibold text-gray-700 mb-2",children:[x.label," ",(0,n.jsx)("span",{className:"text-red-600",children:"*"})]}),(0,n.jsx)("input",{type:x.type,id:"identifier",name:"identifier",value:e.identifier,onChange:u,required:!0,placeholder:x.placeholder,className:"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400"}),(0,n.jsx)("div",{className:"text-sm text-gray-500 italic mt-1",children:x.help})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("label",{htmlFor:"amount",className:"block font-semibold text-gray-700 mb-2",children:"Amount (THB)"}),(0,n.jsx)("input",{type:"number",id:"amount",name:"amount",value:e.amount,onChange:u,step:"0.01",min:"0",placeholder:"0.00",className:"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400"}),(0,n.jsx)("div",{className:"text-sm text-gray-500 italic mt-1",children:"Leave blank for flexible amount"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"merchantName",className:"block font-semibold text-gray-700 mb-2",children:"Merchant Name"}),(0,n.jsx)("input",{type:"text",id:"merchantName",name:"merchantName",value:e.merchantName,onChange:u,placeholder:"Optional",className:"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"merchantCity",className:"block font-semibold text-gray-700 mb-2",children:"Merchant City"}),(0,n.jsx)("input",{type:"text",id:"merchantCity",name:"merchantCity",value:e.merchantCity,onChange:u,placeholder:"Optional",className:"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400"})]})]}),(0,n.jsx)("button",{type:"submit",disabled:r,className:"w-full py-4 bg-gradient-to-r from-indigo-400 to-purple-500 text-white rounded-xl font-semibold text-lg shadow-md hover:shadow-xl hover:scale-[1.01] transition-all duration-200 mt-2 disabled:opacity-60",children:r?"Generating...":"Generate QR Code"})]})}),(0,n.jsxs)("div",{className:"p-8 flex flex-col items-center justify-center bg-white",children:[!a&&!r&&(0,n.jsxs)("div",{className:"empty-state text-center",children:[(0,n.jsx)("div",{className:"text-5xl mb-4 opacity-30",children:"\uD83D\uDCF1"}),(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-700",children:"Generate Your QR Code"}),(0,n.jsx)("p",{className:"text-gray-500",children:"Fill in the form to create a PromptPay QR code for easy payments"})]}),r&&(0,n.jsxs)("div",{className:"loading text-center py-10",children:[(0,n.jsx)("div",{className:"w-12 h-12 border-4 border-gray-200 border-t-blue-400 rounded-full animate-spin mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Generating QR Code..."})]}),a&&(0,n.jsxs)("div",{className:"qr-container text-center py-5",children:[(0,n.jsx)("div",{className:"qr-code bg-white p-5 rounded-xl shadow-lg mb-5 inline-block",children:(0,n.jsx)("img",{src:a.qrCodeDataURL,alt:"PromptPay QR Code",className:"max-w-full h-auto rounded-lg"})}),(0,n.jsxs)("div",{className:"flex flex-wrap justify-center gap-2 mb-4",children:[(0,n.jsx)("button",{onClick:()=>{if(a){let e=document.createElement("a");e.download="promptpay-qr-".concat(Date.now(),".png"),e.href=a.qrCodeDataURL,e.click()}},className:"download-btn bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition-all",children:"\uD83D\uDCE5 Download PNG"}),(0,n.jsx)("button",{onClick:f,className:"copy-btn bg-cyan-600 hover:bg-cyan-700 text-white px-6 py-2 rounded-lg font-semibold transition-all",children:"\uD83D\uDCCB Copy Base64"})]}),(0,n.jsxs)("div",{className:"payment-info bg-gray-100 p-5 rounded-lg mt-4 w-full max-w-lg text-left",children:[(0,n.jsx)("h3",{className:"text-lg font-bold text-gray-700 mb-3",children:"Payment Information"}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"info-item",children:[(0,n.jsx)("span",{className:"label",children:"Type:"}),(0,n.jsx)("span",{className:"value",children:function(e){switch(e){case"mobile":return"Mobile Number";case"national_id":return"National ID";case"ewallet":return"E-Wallet";default:return e}}(a.paymentInfo.type)})]}),(0,n.jsxs)("div",{className:"info-item",children:[(0,n.jsx)("span",{className:"label",children:"Identifier:"}),(0,n.jsx)("span",{className:"value",children:a.paymentInfo.identifier})]}),a.paymentInfo.amount&&(0,n.jsxs)("div",{className:"info-item",children:[(0,n.jsx)("span",{className:"label",children:"Amount:"}),(0,n.jsxs)("span",{className:"value",children:["฿",a.paymentInfo.amount.toFixed(2)]})]}),a.paymentInfo.merchantName&&(0,n.jsxs)("div",{className:"info-item",children:[(0,n.jsx)("span",{className:"label",children:"Merchant:"}),(0,n.jsx)("span",{className:"value",children:a.paymentInfo.merchantName})]}),a.paymentInfo.merchantCity&&(0,n.jsxs)("div",{className:"info-item",children:[(0,n.jsx)("span",{className:"label",children:"City:"}),(0,n.jsx)("span",{className:"value",children:a.paymentInfo.merchantCity})]}),(0,n.jsxs)("div",{className:"info-item",children:[(0,n.jsx)("span",{className:"label",children:"Payload:"}),(0,n.jsx)("span",{className:"value font-mono text-xs break-all",children:a.payload})]})]})]})]}),o&&(0,n.jsx)("div",{className:"error bg-red-100 text-red-700 p-4 rounded-lg mt-4 border border-red-200 w-full max-w-lg text-center",children:o}),c&&(0,n.jsx)("div",{className:"success bg-green-100 text-green-700 p-4 rounded-lg mt-4 border border-green-200 w-full max-w-lg text-center",children:c})]})]})]})}function o(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(r(),{children:[(0,n.jsx)("title",{children:"PromptPay QR Code Generator"}),(0,n.jsx)("meta",{name:"description",content:"Generate QR codes for Thai PromptPay payments instantly"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsx)("main",{className:"bg-gradient-to-br from-indigo-400 to-purple-500 min-h-screen p-5 font-sans",children:(0,n.jsx)(i,{})})]})}},2936:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return a(1935)}])},5648:(e,t)=>{"use strict";function a(e){let{ampFirst:t=!1,hybrid:a=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||a&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return a}})},7328:(e,t,a)=>{e.exports=a(9836)},9836:(e,t,a)=>{"use strict";var n=a(5364);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return x},defaultHead:function(){return u}});let l=a(4252),r=a(8365),s=a(7876),i=r._(a(4232)),o=l._(a(1393)),d=a(9896),c=a(6834),m=a(5648);function u(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}a(546);let h=["name","httpEquiv","charSet","itemProp"];function f(e,t){let{inAmpMode:a}=t;return e.reduce(p,[]).reverse().concat(u(a).reverse()).filter(function(){let e=new Set,t=new Set,a=new Set,n={};return l=>{let r=!0,s=!1;if(l.key&&"number"!=typeof l.key&&l.key.indexOf("$")>0){s=!0;let t=l.key.slice(l.key.indexOf("$")+1);e.has(t)?r=!1:e.add(t)}switch(l.type){case"title":case"base":t.has(l.type)?r=!1:t.add(l.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(l.props.hasOwnProperty(t))if("charSet"===t)a.has(t)?r=!1:a.add(t);else{let e=l.props[t],a=n[t]||new Set;("name"!==t||!s)&&a.has(e)?r=!1:(a.add(e),n[t]=a)}}}return r}}()).reverse().map((e,t)=>{let l=e.key||t;if(n.env.__NEXT_OPTIMIZE_FONTS&&!a&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:l})})}let x=function(e){let{children:t}=e,a=(0,i.useContext)(d.AmpStateContext),n=(0,i.useContext)(c.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:f,headManager:n,inAmpMode:(0,m.isInAmpMode)(a),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9896:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=a(4252)._(a(4232)).default.createContext({})}},e=>{var t=t=>e(e.s=t);e.O(0,[636,593,792],()=>t(2936)),_N_E=e.O()}]);