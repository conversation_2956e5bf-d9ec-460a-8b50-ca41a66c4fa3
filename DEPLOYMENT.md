# Vercel Deployment Guide

This guide will walk you through deploying the PromptPay QR Generator to Vercel.

## Prerequisites

- GitHub account
- Vercel account (free tier available)
- Git repository with your code

## Step-by-Step Deployment

### 1. Prepare Your Repository

Ensure your repository has:
- `package.json` with correct scripts
- `vercel.json` configuration
- `.env.production` file (optional)
- All source code committed

### 2. Connect to Vercel

#### Option A: Vercel Dashboard (Recommended)

1. Go to [vercel.com](https://vercel.com)
2. Sign up/Login with GitHub
3. Click "New Project"
4. Import your repository
5. Configure project settings:
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Install Command**: `npm install`

#### Option B: Vercel CLI

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy from project directory
vercel

# Follow the prompts:
# ? Set up and deploy "~/your-project"? [Y/n] y
# ? Which scope do you want to deploy to? [Your Account]
# ? Link to existing project? [y/N] n
# ? What's your project's name? promptpay-qr-generator
# ? In which directory is your code located? ./
```

### 3. Configure Environment Variables

In Vercel Dashboard:

1. Go to your project
2. Click "Settings" tab
3. Click "Environment Variables"
4. Add the following variables:

```
NEXT_PUBLIC_APP_NAME=PromptPay QR Generator
NEXT_PUBLIC_APP_VERSION=1.0.0
QR_DEFAULT_WIDTH=256
QR_DEFAULT_QUALITY=0.92
QR_DEFAULT_MARGIN=1
NODE_ENV=production
```

### 4. Deploy

#### Automatic Deployment
- Push to your main branch
- Vercel automatically deploys

#### Manual Deployment
```bash
# Deploy to production
npm run deploy

# Deploy to preview
npm run deploy:preview
```

### 5. Custom Domain (Optional)

1. In Vercel Dashboard, go to "Settings" > "Domains"
2. Add your custom domain
3. Configure DNS records as instructed
4. Wait for SSL certificate provisioning

## Vercel Configuration Explained

### `vercel.json`

```json
{
  "version": 2,
  "framework": "nextjs",
  "functions": {
    "pages/api/**/*.js": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/health",
      "destination": "/api/health"
    }
  ]
}
```

**Key Features:**
- **Framework**: Tells Vercel this is a Next.js project
- **Functions**: Sets 30-second timeout for API routes
- **Headers**: Adds CORS headers for API endpoints
- **Rewrites**: Creates friendly URLs for API endpoints

### Build Configuration

Vercel automatically detects Next.js and uses:
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Node.js Version**: 18.x (latest LTS)

## Testing Your Deployment

### 1. Check Deployment Status
- Monitor build logs in Vercel Dashboard
- Verify deployment URL works

### 2. Test API Endpoints
```bash
# Replace YOUR_DOMAIN with your Vercel URL
curl https://YOUR_DOMAIN.vercel.app/api/health

curl -X POST https://YOUR_DOMAIN.vercel.app/api/generate-qr \
  -H "Content-Type: application/json" \
  -d '{"type":"mobile","identifier":"**********","amount":100}'
```

### 3. Test Web Interface
- Open your deployment URL
- Test QR code generation
- Verify download functionality

## Monitoring and Maintenance

### 1. Analytics
- Enable Vercel Analytics in project settings
- Monitor performance and usage

### 2. Logs
- View function logs in Vercel Dashboard
- Monitor for errors and performance issues

### 3. Updates
- Push to main branch for automatic deployment
- Use preview deployments for testing

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are in package.json
   - Review build logs for specific errors

2. **API Timeouts**
   - Ensure functions complete within 30 seconds
   - Optimize QR generation code
   - Consider caching strategies

3. **Environment Variables**
   - Verify variables are set in Vercel Dashboard
   - Check variable names match code
   - Redeploy after adding variables

4. **CORS Issues**
   - Verify CORS headers in API routes
   - Check vercel.json configuration
   - Test with different origins

### Getting Help

- Check Vercel documentation
- Review deployment logs
- Contact Vercel support for platform issues
- Create GitHub issues for code problems

## Performance Optimization

### 1. Image Optimization
- Use Next.js Image component
- Optimize QR code generation

### 2. Caching
- Implement API response caching
- Use Vercel Edge Cache

### 3. Bundle Analysis
```bash
npm run analyze
```

## Security Considerations

1. **Environment Variables**: Never commit secrets to repository
2. **API Rate Limiting**: Consider implementing rate limiting
3. **Input Validation**: Validate all API inputs
4. **CORS**: Configure appropriate CORS policies

## Cost Optimization

- Monitor function execution time
- Optimize cold start performance
- Use appropriate Vercel plan for your usage
