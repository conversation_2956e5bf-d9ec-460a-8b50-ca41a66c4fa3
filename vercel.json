{"version": 2, "framework": "nextjs", "functions": {"pages/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/health", "destination": "/api/health"}, {"source": "/examples", "destination": "/api/examples"}]}