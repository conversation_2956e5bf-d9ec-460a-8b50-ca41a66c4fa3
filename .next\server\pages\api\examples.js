"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/examples";
exports.ids = ["pages/api/examples"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fexamples&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cexamples.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fexamples&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cexamples.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_examples_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\examples.js */ \"(api-node)/./pages/api/examples.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_examples_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_examples_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/examples\",\n        pathname: \"/api/examples\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_examples_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGZXhhbXBsZXMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q2FwaSU1Q2V4YW1wbGVzLmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxtREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsbURBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxhcGlcXFxcZXhhbXBsZXMuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2V4YW1wbGVzXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvZXhhbXBsZXNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fexamples&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cexamples.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/examples.js":
/*!*******************************!*\
  !*** ./pages/api/examples.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\nfunction handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    res.json({\n        examples: [\n            {\n                description: 'Generate QR for mobile number',\n                method: 'POST',\n                endpoint: '/api/generate-qr',\n                body: {\n                    type: 'mobile',\n                    identifier: '0812345678',\n                    amount: 100.50,\n                    merchantName: 'John Doe',\n                    merchantCity: 'Bangkok'\n                }\n            },\n            {\n                description: 'Generate QR for National ID',\n                method: 'POST',\n                endpoint: '/api/generate-qr',\n                body: {\n                    type: 'national_id',\n                    identifier: '1234567890123',\n                    amount: 250.00\n                }\n            },\n            {\n                description: 'Generate QR for e-Wallet',\n                method: 'POST',\n                endpoint: '/api/generate-qr',\n                body: {\n                    type: 'ewallet',\n                    identifier: '1234567890',\n                    merchantName: 'Shop ABC'\n                }\n            }\n        ]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/examples.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fexamples&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cexamples.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();