"use strict";(()=>{var e={};e.id=74,e.ids=[74],e.modules={1863:(e,t,n)=>{n.r(t),n.d(t,{config:()=>l,default:()=>s,routeModule:()=>u});var r={};n.r(r),n.d(r,{default:()=>d});var o=n(3480),i=n(8667),a=n(6435);function d(e,t){return(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type"),"OPTIONS"===e.method)?t.status(200).end():"GET"!==e.method?t.status(405).json({error:"Method not allowed"}):void t.json({examples:[{description:"Generate QR for mobile number",method:"POST",endpoint:"/api/generate-qr",body:{type:"mobile",identifier:"0812345678",amount:100.5,merchantName:"<PERSON>",merchantCity:"Bangkok"}},{description:"Generate QR for National ID",method:"POST",endpoint:"/api/generate-qr",body:{type:"national_id",identifier:"1234567890123",amount:250}},{description:"Generate QR for e-Wallet",method:"POST",endpoint:"/api/generate-qr",body:{type:"ewallet",identifier:"1234567890",merchantName:"Shop ABC"}}]})}let s=(0,a.M)(r,"default"),l=(0,a.M)(r,"config"),u=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/examples",pathname:"/api/examples",bundlePath:"",filename:""},userland:r})},3480:(e,t,n)=>{e.exports=n(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var n=t(t.s=1863);module.exports=n})();