const QRCode = require('qrcode');
const generatePayload = require('promptpay-qr');

// CORS headers for Vercel deployment
const setCorsHeaders = (res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
};

export default async function handler(req, res) {
  // Set CORS headers
  setCorsHeaders(res);

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      type = 'mobile',
      identifier,
      amount,
      merchantName,
      merchantCity,
      qrOptions = {}
    } = req.body;

    // Validate required fields
    if (!identifier) {
      return res.status(400).json({
        error: 'Identifier is required (mobile number, national ID, or e-wallet ID)'
      });
    }

    // Validate and format identifier for each type
    let formattedIdentifier = identifier;
    if (type === 'national_id') {
      // National ID must be 13 digits
      formattedIdentifier = String(identifier).replace(/\D/g, '').padStart(13, '0').slice(0, 13);
      if (formattedIdentifier.length !== 13) {
        return res.status(400).json({ error: 'National ID must be 13 digits' });
      }
    } else if (type === 'ewallet') {
      // E-Wallet must be 15 digits
      formattedIdentifier = String(identifier).replace(/\D/g, '').padStart(15, '0').slice(0, 15);
      if (formattedIdentifier.length !== 15) {
        return res.status(400).json({ error: 'E-Wallet ID must be 15 digits' });
      }
    }

    // Generate PromptPay payload using promptpay-qr
    let payload;
    if (type === 'mobile' || type === 'national_id' || type === 'ewallet') {
      payload = generatePayload(formattedIdentifier, { amount });
    } else {
      return res.status(400).json({ error: 'Invalid PromptPay type' });
    }

    // QR Code generation options with environment variable support
    const defaultQROptions = {
      type: 'image/png',
      quality: parseFloat(process.env.QR_DEFAULT_QUALITY) || 0.92,
      margin: parseInt(process.env.QR_DEFAULT_MARGIN) || 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: parseInt(process.env.QR_DEFAULT_WIDTH) || 256,
      ...qrOptions
    };

    // Generate QR code as base64
    const qrCodeDataURL = await QRCode.toDataURL(payload, defaultQROptions);
    const base64Image = qrCodeDataURL.split(',')[1];

    res.json({
      success: true,
      data: {
        payload,
        qrCodeBase64: base64Image,
        qrCodeDataURL,
        paymentInfo: {
          type,
          identifier,
          amount: amount || null,
          merchantName: merchantName || null,
          merchantCity: merchantCity || null
        }
      }
    });

  } catch (error) {
    console.error('Error generating QR code:', error);
    res.status(500).json({
      error: 'Failed to generate QR code',
      message: error.message
    });
  }
}
