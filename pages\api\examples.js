export default function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  res.json({
    examples: [
      {
        description: 'Generate QR for mobile number',
        method: 'POST',
        endpoint: '/api/generate-qr',
        body: {
          type: 'mobile',
          identifier: '0812345678',
          amount: 100.50,
          merchantName: '<PERSON>',
          merchantCity: 'Bangkok'
        }
      },
      {
        description: 'Generate QR for National ID',
        method: 'POST',
        endpoint: '/api/generate-qr',
        body: {
          type: 'national_id',
          identifier: '1234567890123',
          amount: 250.00
        }
      },
      {
        description: 'Generate QR for e-Wallet',
        method: 'POST',
        endpoint: '/api/generate-qr',
        body: {
          type: 'ewallet',
          identifier: '1234567890',
          merchantName: 'Shop ABC'
        }
      }
    ]
  });
}
