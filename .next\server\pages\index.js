/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./components/QRGenerator.js":
/*!***********************************!*\
  !*** ./components/QRGenerator.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction QRGenerator() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        identifier: '',\n        amount: '',\n        merchantName: '',\n        merchantCity: ''\n    });\n    const [qrData, setQrData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear identifier when type changes\n        if (name === 'type') {\n            setFormData((prev)=>({\n                    ...prev,\n                    identifier: ''\n                }));\n        }\n    };\n    const getIdentifierConfig = ()=>{\n        switch(formData.type){\n            case 'mobile':\n                return {\n                    label: 'Mobile Number',\n                    placeholder: 'e.g., 0812345678 or +66812345678',\n                    help: 'Enter Thai mobile number (with or without country code)',\n                    type: 'tel'\n                };\n            case 'national_id':\n                return {\n                    label: 'National ID',\n                    placeholder: 'e.g., 1234567890123',\n                    help: 'Enter 13-digit Thai National ID (with or without dashes)',\n                    type: 'text'\n                };\n            case 'ewallet':\n                return {\n                    label: 'E-Wallet ID',\n                    placeholder: 'e.g., 1234567890',\n                    help: 'Enter your e-wallet identifier',\n                    type: 'text'\n                };\n            default:\n                return {\n                    label: 'Identifier',\n                    placeholder: 'Enter identifier',\n                    help: 'Please select a payment type first',\n                    type: 'text'\n                };\n        }\n    };\n    const validateForm = ()=>{\n        const { type, identifier } = formData;\n        if (!type) {\n            return 'Please select a payment type.';\n        }\n        if (!identifier.trim()) {\n            return 'Identifier is required.';\n        }\n        if (type === 'national_id') {\n            const digits = identifier.replace(/\\D/g, '');\n            if (digits.length !== 13) {\n                return 'National ID must be exactly 13 digits.';\n            }\n        } else if (type === 'ewallet') {\n            const digits = identifier.replace(/\\D/g, '');\n            if (digits.length !== 15) {\n                return 'E-Wallet ID must be exactly 15 digits.';\n            }\n        } else if (type === 'mobile') {\n            const digits = identifier.replace(/\\D/g, '');\n            if (digits.length < 9 || digits.length > 10) {\n                return 'Mobile number should be 9 or 10 digits.';\n            }\n        }\n        return null;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            return;\n        }\n        setLoading(true);\n        try {\n            const requestData = {\n                type: formData.type,\n                identifier: formData.identifier,\n                amount: formData.amount ? parseFloat(formData.amount) : null,\n                merchantName: formData.merchantName || null,\n                merchantCity: formData.merchantCity || null\n            };\n            const response = await fetch('/api/generate-qr', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setQrData(result.data);\n                setSuccess('QR Code generated successfully!');\n            } else {\n                setError(result.error || 'Failed to generate QR code');\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            setError('Network error. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const downloadQR = ()=>{\n        if (qrData) {\n            const link = document.createElement('a');\n            link.download = `promptpay-qr-${Date.now()}.png`;\n            link.href = qrData.qrCodeDataURL;\n            link.click();\n        }\n    };\n    const copyBase64 = async ()=>{\n        if (qrData) {\n            try {\n                await navigator.clipboard.writeText(qrData.qrCodeBase64);\n                setSuccess('Base64 data copied to clipboard!');\n            } catch (error) {\n                // Fallback for older browsers\n                const textArea = document.createElement('textarea');\n                textArea.value = qrData.qrCodeBase64;\n                document.body.appendChild(textArea);\n                textArea.select();\n                document.execCommand('copy');\n                document.body.removeChild(textArea);\n                setSuccess('Base64 data copied to clipboard!');\n            }\n        }\n    };\n    const identifierConfig = getIdentifierConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container max-w-4xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-400 to-cyan-300 text-white p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl md:text-4xl font-bold mb-2\",\n                        children: \"PromptPay QR Generator\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg opacity-90\",\n                        children: \"Generate QR codes for Thai PromptPay payments instantly\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 min-h-[600px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"type\",\n                                            className: \"block font-semibold text-gray-700 mb-2\",\n                                            children: [\n                                                \"Payment Type \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 30\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"type\",\n                                            name: \"type\",\n                                            value: formData.type,\n                                            onChange: handleInputChange,\n                                            required: true,\n                                            className: \"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select payment type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"mobile\",\n                                                    children: \"Mobile Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"national_id\",\n                                                    children: \"National ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ewallet\",\n                                                    children: \"E-Wallet ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"identifier\",\n                                            className: \"block font-semibold text-gray-700 mb-2\",\n                                            children: [\n                                                identifierConfig.label,\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 42\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: identifierConfig.type,\n                                            id: \"identifier\",\n                                            name: \"identifier\",\n                                            value: formData.identifier,\n                                            onChange: handleInputChange,\n                                            required: true,\n                                            placeholder: identifierConfig.placeholder,\n                                            className: \"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 italic mt-1\",\n                                            children: identifierConfig.help\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"amount\",\n                                            className: \"block font-semibold text-gray-700 mb-2\",\n                                            children: \"Amount (THB)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            id: \"amount\",\n                                            name: \"amount\",\n                                            value: formData.amount,\n                                            onChange: handleInputChange,\n                                            step: \"0.01\",\n                                            min: \"0\",\n                                            placeholder: \"0.00\",\n                                            className: \"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 italic mt-1\",\n                                            children: \"Leave blank for flexible amount\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"merchantName\",\n                                                    className: \"block font-semibold text-gray-700 mb-2\",\n                                                    children: \"Merchant Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"merchantName\",\n                                                    name: \"merchantName\",\n                                                    value: formData.merchantName,\n                                                    onChange: handleInputChange,\n                                                    placeholder: \"Optional\",\n                                                    className: \"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"merchantCity\",\n                                                    className: \"block font-semibold text-gray-700 mb-2\",\n                                                    children: \"Merchant City\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"merchantCity\",\n                                                    name: \"merchantCity\",\n                                                    value: formData.merchantCity,\n                                                    onChange: handleInputChange,\n                                                    placeholder: \"Optional\",\n                                                    className: \"w-full p-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full py-4 bg-gradient-to-r from-indigo-400 to-purple-500 text-white rounded-xl font-semibold text-lg shadow-md hover:shadow-xl hover:scale-[1.01] transition-all duration-200 mt-2 disabled:opacity-60\",\n                                    children: loading ? 'Generating...' : 'Generate QR Code'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 flex flex-col items-center justify-center bg-white\",\n                        children: [\n                            !qrData && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"empty-state text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-5xl mb-4 opacity-30\",\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-2 text-gray-700\",\n                                        children: \"Generate Your QR Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Fill in the form to create a PromptPay QR code for easy payments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading text-center py-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 border-4 border-gray-200 border-t-blue-400 rounded-full animate-spin mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Generating QR Code...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            qrData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"qr-container text-center py-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"qr-code bg-white p-5 rounded-xl shadow-lg mb-5 inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: qrData.qrCodeDataURL,\n                                            alt: \"PromptPay QR Code\",\n                                            className: \"max-w-full h-auto rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: downloadQR,\n                                                className: \"download-btn bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition-all\",\n                                                children: \"\\uD83D\\uDCE5 Download PNG\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyBase64,\n                                                className: \"copy-btn bg-cyan-600 hover:bg-cyan-700 text-white px-6 py-2 rounded-lg font-semibold transition-all\",\n                                                children: \"\\uD83D\\uDCCB Copy Base64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"payment-info bg-gray-100 p-5 rounded-lg mt-4 w-full max-w-lg text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-700 mb-3\",\n                                                children: \"Payment Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"label\",\n                                                                children: \"Type:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"value\",\n                                                                children: formatPaymentType(qrData.paymentInfo.type)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"label\",\n                                                                children: \"Identifier:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"value\",\n                                                                children: qrData.paymentInfo.identifier\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    qrData.paymentInfo.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"label\",\n                                                                children: \"Amount:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"value\",\n                                                                children: [\n                                                                    \"฿\",\n                                                                    qrData.paymentInfo.amount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    qrData.paymentInfo.merchantName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"label\",\n                                                                children: \"Merchant:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"value\",\n                                                                children: qrData.paymentInfo.merchantName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    qrData.paymentInfo.merchantCity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"label\",\n                                                                children: \"City:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"value\",\n                                                                children: qrData.paymentInfo.merchantCity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"label\",\n                                                                children: \"Payload:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"value font-mono text-xs break-all\",\n                                                                children: qrData.payload\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error bg-red-100 text-red-700 p-4 rounded-lg mt-4 border border-red-200 w-full max-w-lg text-center\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"success bg-green-100 text-green-700 p-4 rounded-lg mt-4 border border-green-200 w-full max-w-lg text-center\",\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\components\\\\QRGenerator.js\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\nfunction formatPaymentType(type) {\n    switch(type){\n        case 'mobile':\n            return 'Mobile Number';\n        case 'national_id':\n            return 'National ID';\n        case 'ewallet':\n            return 'E-Wallet';\n        default:\n            return type;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/QRGenerator.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.js */ \"(pages-dir-node)/./pages/index.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRWYsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUml0aFxcRGVza3RvcFxcRXBvc3NlcnZpY2VcXG5leHRfc2ltcGxlX3dlYl9nZW5lcmF0ZV9xclxccGFnZXNcXF9hcHAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgcmV0dXJuIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbn1cbiJdLCJuYW1lcyI6WyJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_QRGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/QRGenerator */ \"(pages-dir-node)/./components/QRGenerator.js\");\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"PromptPay QR Code Generator\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Generate QR codes for Thai PromptPay payments instantly\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"bg-gradient-to-br from-indigo-400 to-purple-500 min-h-screen p-5 font-sans\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRGenerator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Eposservice\\\\next_simple_web_generate_qr\\\\pages\\\\index.js\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEI7QUFDdUI7QUFFcEMsU0FBU0U7SUFDdEIscUJBQ0U7OzBCQUNFLDhEQUFDRixrREFBSUE7O2tDQUNILDhEQUFDRztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBR3hCLDhEQUFDQztnQkFBS0MsV0FBVTswQkFDZCw0RUFBQ1YsK0RBQVdBOzs7Ozs7Ozs7Ozs7QUFJcEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUml0aFxcRGVza3RvcFxcRXBvc3NlcnZpY2VcXG5leHRfc2ltcGxlX3dlYl9nZW5lcmF0ZV9xclxccGFnZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCdcbmltcG9ydCBRUkdlbmVyYXRvciBmcm9tICcuLi9jb21wb25lbnRzL1FSR2VuZXJhdG9yJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPlByb21wdFBheSBRUiBDb2RlIEdlbmVyYXRvcjwvdGl0bGU+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJHZW5lcmF0ZSBRUiBjb2RlcyBmb3IgVGhhaSBQcm9tcHRQYXkgcGF5bWVudHMgaW5zdGFudGx5XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWluZGlnby00MDAgdG8tcHVycGxlLTUwMCBtaW4taC1zY3JlZW4gcC01IGZvbnQtc2Fuc1wiPlxuICAgICAgICA8UVJHZW5lcmF0b3IgLz5cbiAgICAgIDwvbWFpbj5cbiAgICA8Lz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkhlYWQiLCJRUkdlbmVyYXRvciIsIkhvbWUiLCJ0aXRsZSIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwibWFpbiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();