"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/generate-qr";
exports.ids = ["pages/api/generate-qr"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate-qr&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgenerate-qr.js&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate-qr&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgenerate-qr.js&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_generate_qr_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\generate-qr.js */ \"(api-node)/./pages/api/generate-qr.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_generate_qr_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_generate_qr_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/generate-qr\",\n        pathname: \"/api/generate-qr\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_generate_qr_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGZ2VuZXJhdGUtcXImcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q2FwaSU1Q2dlbmVyYXRlLXFyLmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQ3lEO0FBQ3pEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxzREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsc0RBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxhcGlcXFxcZ2VuZXJhdGUtcXIuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2dlbmVyYXRlLXFyXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvZ2VuZXJhdGUtcXJcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate-qr&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgenerate-qr.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/generate-qr.js":
/*!**********************************!*\
  !*** ./pages/api/generate-qr.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\nconst QRCode = __webpack_require__(/*! qrcode */ \"qrcode\");\nconst generatePayload = __webpack_require__(/*! promptpay-qr */ \"promptpay-qr\");\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const { type = 'mobile', identifier, amount, merchantName, merchantCity, qrOptions = {} } = req.body;\n        // Validate required fields\n        if (!identifier) {\n            return res.status(400).json({\n                error: 'Identifier is required (mobile number, national ID, or e-wallet ID)'\n            });\n        }\n        // Validate and format identifier for each type\n        let formattedIdentifier = identifier;\n        if (type === 'national_id') {\n            // National ID must be 13 digits\n            formattedIdentifier = String(identifier).replace(/\\D/g, '').padStart(13, '0').slice(0, 13);\n            if (formattedIdentifier.length !== 13) {\n                return res.status(400).json({\n                    error: 'National ID must be 13 digits'\n                });\n            }\n        } else if (type === 'ewallet') {\n            // E-Wallet must be 15 digits\n            formattedIdentifier = String(identifier).replace(/\\D/g, '').padStart(15, '0').slice(0, 15);\n            if (formattedIdentifier.length !== 15) {\n                return res.status(400).json({\n                    error: 'E-Wallet ID must be 15 digits'\n                });\n            }\n        }\n        // Generate PromptPay payload using promptpay-qr\n        let payload;\n        if (type === 'mobile' || type === 'national_id' || type === 'ewallet') {\n            payload = generatePayload(formattedIdentifier, {\n                amount\n            });\n        } else {\n            return res.status(400).json({\n                error: 'Invalid PromptPay type'\n            });\n        }\n        // QR Code generation options\n        const defaultQROptions = {\n            type: 'image/png',\n            quality: 0.92,\n            margin: 1,\n            color: {\n                dark: '#000000',\n                light: '#FFFFFF'\n            },\n            width: 256,\n            ...qrOptions\n        };\n        // Generate QR code as base64\n        const qrCodeDataURL = await QRCode.toDataURL(payload, defaultQROptions);\n        const base64Image = qrCodeDataURL.split(',')[1];\n        res.json({\n            success: true,\n            data: {\n                payload,\n                qrCodeBase64: base64Image,\n                qrCodeDataURL,\n                paymentInfo: {\n                    type,\n                    identifier,\n                    amount: amount || null,\n                    merchantName: merchantName || null,\n                    merchantCity: merchantCity || null\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error generating QR code:', error);\n        res.status(500).json({\n            error: 'Failed to generate QR code',\n            message: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/generate-qr.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "promptpay-qr":
/*!*******************************!*\
  !*** external "promptpay-qr" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("promptpay-qr");

/***/ }),

/***/ "qrcode":
/*!*************************!*\
  !*** external "qrcode" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("qrcode");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate-qr&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgenerate-qr.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();