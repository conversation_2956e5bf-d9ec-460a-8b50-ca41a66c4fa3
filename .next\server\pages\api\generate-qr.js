"use strict";(()=>{var e={};e.id=966,e.ids=[966],e.modules={3480:(e,t,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5634:e=>{e.exports=require("qrcode")},5732:e=>{e.exports=require("promptpay-qr")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9758:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>c,routeModule:()=>A});var n={};r.r(n),r.d(n,{default:()=>d});var a=r(3480),o=r(8667),s=r(6435);let i=r(5634),l=r(5732),u=e=>{e.setHeader("Access-Control-Allow-Origin","*"),e.setHeader("Access-Control-Allow-Methods","POST, OPTIONS"),e.setHeader("Access-Control-Allow-Headers","Content-Type")};async function d(e,t){if(u(t),"OPTIONS"===e.method)return t.status(200).end();if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r,{type:n="mobile",identifier:a,amount:o,merchantName:s,merchantCity:u,qrOptions:d={}}=e.body;if(!a)return t.status(400).json({error:"Identifier is required (mobile number, national ID, or e-wallet ID)"});let c=a;if("national_id"===n){if(c=String(a).replace(/\D/g,"").padStart(13,"0").slice(0,13),13!==c.length)return t.status(400).json({error:"National ID must be 13 digits"})}else if("ewallet"===n&&(c=String(a).replace(/\D/g,"").padStart(15,"0").slice(0,15),15!==c.length))return t.status(400).json({error:"E-Wallet ID must be 15 digits"});if("mobile"!==n&&"national_id"!==n&&"ewallet"!==n)return t.status(400).json({error:"Invalid PromptPay type"});r=l(c,{amount:o});let p={type:"image/png",quality:parseFloat(process.env.QR_DEFAULT_QUALITY)||.92,margin:parseInt(process.env.QR_DEFAULT_MARGIN)||1,color:{dark:"#000000",light:"#FFFFFF"},width:parseInt(process.env.QR_DEFAULT_WIDTH)||256,...d},A=await i.toDataURL(r,p),P=A.split(",")[1];t.json({success:!0,data:{payload:r,qrCodeBase64:P,qrCodeDataURL:A,paymentInfo:{type:n,identifier:a,amount:o||null,merchantName:s||null,merchantCity:u||null}}})}catch(e){console.error("Error generating QR code:",e),t.status(500).json({error:"Failed to generate QR code",message:e.message})}}let c=(0,s.M)(n,"default"),p=(0,s.M)(n,"config"),A=new a.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/generate-qr",pathname:"/api/generate-qr",bundlePath:"",filename:""},userland:n})}};var t=require("../../webpack-api-runtime.js");t.C(e);var r=t(t.s=9758);module.exports=r})();