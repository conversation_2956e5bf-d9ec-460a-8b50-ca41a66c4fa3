# PromptPay QR Generator - Next.js

A modern web application built with Next.js for generating QR codes for Thai PromptPay payments. Supports mobile numbers, national IDs, and e-wallet IDs with customizable payment amounts.

## 🚀 Features

- **Multiple Payment Types**: Mobile number, National ID, E-Wallet ID
- **Flexible Amounts**: Fixed or open amounts
- **QR Code Generation**: High-quality PNG QR codes
- **Download & Copy**: Download PNG or copy base64 data
- **Responsive Design**: Works on desktop and mobile
- **API Endpoints**: RESTful API for integration
- **Serverless Ready**: Optimized for Vercel deployment

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Styling**: Tailwind CSS
- **QR Generation**: qrcode library
- **PromptPay**: promptpay-qr library
- **Deployment**: Vercel

## 📦 Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd next_simple_web_generate_qr
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🌐 Deployment on Vercel

### Quick Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/next_simple_web_generate_qr)

### Manual Deployment

1. **Install Vercel CLI**:
```bash
npm install -g vercel
```

2. **Login to Vercel**:
```bash
vercel login
```

3. **Deploy to preview**:
```bash
npm run deploy:preview
```

4. **Deploy to production**:
```bash
npm run deploy
```

### Environment Variables on Vercel

Set these environment variables in your Vercel dashboard:

```
NEXT_PUBLIC_APP_NAME=PromptPay QR Generator
NEXT_PUBLIC_APP_VERSION=1.0.0
QR_DEFAULT_WIDTH=256
QR_DEFAULT_QUALITY=0.92
QR_DEFAULT_MARGIN=1
```

## 📡 API Endpoints

### Generate QR Code
```
POST /api/generate-qr
```

**Request Body**:
```json
{
  "type": "mobile",
  "identifier": "**********",
  "amount": 100.50,
  "merchantName": "John Doe",
  "merchantCity": "Bangkok"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "payload": "00020101021129370016A000000677010111011300668123456785802TH5303764540510063042B09",
    "qrCodeBase64": "iVBORw0KGgoAAAANSUhEUgAA...",
    "qrCodeDataURL": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "paymentInfo": {
      "type": "mobile",
      "identifier": "**********",
      "amount": 100.50,
      "merchantName": "John Doe",
      "merchantCity": "Bangkok"
    }
  }
}
```

### Health Check
```
GET /api/health
```

### API Examples
```
GET /api/examples
```

## 🔧 Configuration

### Vercel Configuration (`vercel.json`)

The project includes optimized Vercel configuration:
- Serverless function timeout: 30 seconds
- CORS headers for API routes
- URL rewrites for backward compatibility

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_APP_NAME` | Application name | PromptPay QR Generator |
| `NEXT_PUBLIC_APP_VERSION` | Application version | 1.0.0 |
| `QR_DEFAULT_WIDTH` | Default QR code width | 256 |
| `QR_DEFAULT_QUALITY` | Default QR code quality | 0.92 |
| `QR_DEFAULT_MARGIN` | Default QR code margin | 1 |

## 🧪 Testing

Run the development server and test the endpoints:

```bash
# Start development server
npm run dev

# Test health endpoint
curl http://localhost:3000/api/health

# Test QR generation
curl -X POST http://localhost:3000/api/generate-qr \
  -H "Content-Type: application/json" \
  -d '{"type":"mobile","identifier":"**********","amount":100}'
```

## 📝 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run deploy` - Deploy to Vercel production
- `npm run deploy:preview` - Deploy to Vercel preview

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🔍 Troubleshooting

### Common Deployment Issues

1. **Build Errors**: Check that all dependencies are installed
2. **API Timeouts**: Ensure functions complete within 30 seconds
3. **Environment Variables**: Verify all required variables are set in Vercel dashboard
4. **CORS Issues**: API routes include proper CORS headers

### Vercel Deployment Checklist

- [ ] Repository connected to Vercel
- [ ] Environment variables configured
- [ ] Build command: `npm run build`
- [ ] Output directory: `.next`
- [ ] Node.js version: 18.x or higher

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the API documentation
- Review the Vercel deployment logs
